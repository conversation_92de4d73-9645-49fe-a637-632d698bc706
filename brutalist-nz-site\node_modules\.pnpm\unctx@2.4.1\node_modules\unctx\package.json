{"name": "unctx", "version": "2.4.1", "description": "Composition-api in Vanilla js", "repository": "unjs/unctx", "license": "MIT", "sideEffects": false, "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.cjs"}, "./transform": {"types": "./dist/transform.d.ts", "import": "./dist/transform.mjs"}, "./plugin": {"types": "./dist/plugin.d.ts", "import": "./dist/plugin.mjs"}}, "main": "./dist/index.cjs", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "typesVersions": {"*": {"*": ["./dist/*", "./dist/index.d.ts"]}}, "files": ["dist"], "scripts": {"build": "unbuild", "dev": "vitest", "lint": "eslint . && prettier -c src test", "lint:fix": "eslint --fix . && prettier -w src test", "prepack": "unbuild", "release": "pnpm test && changelogen --release && npm publish && git push --follow-tags", "test": "pnpm lint && pnpm test:types && vitest run --coverage", "test:types": "tsc --noEmit"}, "dependencies": {"acorn": "^8.14.0", "estree-walker": "^3.0.3", "magic-string": "^0.30.17", "unplugin": "^2.1.0"}, "devDependencies": {"@types/estree": "^1.0.6", "@types/node": "^22.10.2", "@vitest/coverage-v8": "^2.1.8", "changelogen": "^0.5.7", "eslint": "^9.17.0", "eslint-config-unjs": "^0.4.2", "jiti": "^2.4.2", "prettier": "^3.4.2", "typescript": "^5.7.2", "unbuild": "^3.0.1", "vitest": "^2.1.8"}, "packageManager": "pnpm@9.15.0"}