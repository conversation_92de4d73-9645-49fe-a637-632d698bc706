"use strict";
// THIS CODE WAS AUTOMATICALLY GENERATED
// DO NOT EDIT THIS CODE BY HAND
// RUN THE FOLLOWING COMMAND FROM THE WORKSPACE ROOT TO REGENERATE:
// npx nx generate-lib repo
Object.defineProperty(exports, "__esModule", { value: true });
exports.es6 = void 0;
const es5_1 = require("./es5");
const es2015_collection_1 = require("./es2015.collection");
const es2015_core_1 = require("./es2015.core");
const es2015_generator_1 = require("./es2015.generator");
const es2015_iterable_1 = require("./es2015.iterable");
const es2015_promise_1 = require("./es2015.promise");
const es2015_proxy_1 = require("./es2015.proxy");
const es2015_reflect_1 = require("./es2015.reflect");
const es2015_symbol_1 = require("./es2015.symbol");
const es2015_symbol_wellknown_1 = require("./es2015.symbol.wellknown");
exports.es6 = {
    libs: [
        es5_1.es5,
        es2015_core_1.es2015_core,
        es2015_collection_1.es2015_collection,
        es2015_iterable_1.es2015_iterable,
        es2015_generator_1.es2015_generator,
        es2015_promise_1.es2015_promise,
        es2015_proxy_1.es2015_proxy,
        es2015_reflect_1.es2015_reflect,
        es2015_symbol_1.es2015_symbol,
        es2015_symbol_wellknown_1.es2015_symbol_wellknown,
    ],
    variables: [],
};
