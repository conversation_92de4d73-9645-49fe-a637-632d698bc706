{"name": "unplugin-utils", "version": "0.2.4", "description": "A set of utility functions commonly used by unplugins.", "type": "module", "license": "MIT", "homepage": "https://github.com/sxzz/unplugin-utils#readme", "bugs": {"url": "https://github.com/sxzz/unplugin-utils/issues"}, "repository": {"type": "git", "url": "git+https://github.com/sxzz/unplugin-utils.git"}, "author": "三咲智子 <PERSON> <<EMAIL>>", "funding": "https://github.com/sponsors/sxzz", "files": ["dist"], "main": "./dist/index.js", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"require": "./dist/index.cjs", "import": "./dist/index.js"}, "./package.json": "./package.json"}, "publishConfig": {"access": "public"}, "dependencies": {"pathe": "^2.0.2", "picomatch": "^4.0.2"}, "devDependencies": {"@sxzz/eslint-config": "^5.0.1", "@sxzz/prettier-config": "^2.1.1", "@types/node": "^22.13.1", "@types/picomatch": "^3.0.2", "bumpp": "^10.0.2", "eslint": "^9.19.0", "oxc-transform": "^0.48.2", "prettier": "^3.4.2", "tsdown": "^0.5.7", "tsx": "^4.19.2", "typescript": "^5.7.3", "vitest": "^3.0.5"}, "engines": {"node": ">=18.12.0"}, "prettier": "@sxzz/prettier-config", "scripts": {"lint": "eslint --cache .", "lint:fix": "pnpm run lint --fix", "build": "tsdown", "dev": "tsdown --watch", "test": "vitest", "typecheck": "tsc --noEmit", "format": "prettier --cache --write .", "release": "bumpp && pnpm publish"}}