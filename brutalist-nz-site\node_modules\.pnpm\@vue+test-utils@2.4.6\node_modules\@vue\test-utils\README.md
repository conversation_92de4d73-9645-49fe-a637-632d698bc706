# Vue Test Utils

Component testing utils for Vue 3.

## Languages

[🇫🇷 French version of this README.md](https://github.com/vuejs/test-utils/tree/main/docs/fr/README.md)

## Installation and Usage

- yarn: `yarn add @vue/test-utils --dev`
- npm: `npm install @vue/test-utils --save-dev`

Get started with the [documentation](https://test-utils.vuejs.org/).

## Coming from Vue 2 + Test Utils v1?

[Check the migration guide](https://test-utils.vuejs.org/migration/). It's still a work in progress. If you find a problem or something that doesn't work that previously did in Vue Test Utils v1, please open an issue.

## Documentation

See the [docs](https://test-utils.vuejs.org/).

## Development

Get started by running `pnpm install`. You can run the tests with `pnpm test`. That's it!

## Contributing Docs

All the documentation files can be found in `packages/docs`. It contains the English markdown files while translation(s) are stored in their corresponding `<lang>` sub-folder(s):

- [`fr`](https://github.com/vuejs/test-utils/tree/main/packages/docs/fr): French translation.

Besides that, the `.vitepress` sub-folder contains the config and theme, including the i18n information.

- `pnpm docs:dev`: Start the docs dev server.
- `pnpm docs:build`: Build the docs.

To add or maintain the translations, we follow the [Vue Ecosystem Translation Guidelines](https://github.com/vuejs-translations/guidelines/blob/main/README_ECOSYSTEM.md).

- `pnpm docs:translation:status [<lang>]`: Show the translation status for your language. If you don't specify a language, it will show the status for all languages.
- `pnpm docs:translation:compare <lang>`: Compare the docs with the latest checkpoint for your language.
- `pnpm docs:translation:update <lang> [<commit>]`: Update the checkpoint for your language. The checkpoint will be set by the latest commit hash. However, you can also specify a commit hash manually.
