!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?t(exports):"function"==typeof define&&define.amd?define(["exports"],t):t((e="undefined"!=typeof globalThis?globalThis:e||self).framesync={})}(this,(function(e){"use strict";const t=1/60*1e3,n="undefined"!=typeof performance?()=>performance.now():()=>Date.now(),o="undefined"!=typeof window?e=>window.requestAnimationFrame(e):e=>setTimeout((()=>e(n())),t);let s=!0,c=!1,d=!1;const r={delta:0,timestamp:0},a=["read","update","preRender","render","postRender"],f=a.reduce(((e,t)=>(e[t]=function(e){let t=[],n=[],o=0,s=!1,c=!1;const d=new WeakSet,r={schedule:(e,c=!1,r=!1)=>{const a=r&&s,f=a?t:n;return c&&d.add(e),-1===f.indexOf(e)&&(f.push(e),a&&s&&(o=t.length)),e},cancel:e=>{const t=n.indexOf(e);-1!==t&&n.splice(t,1),d.delete(e)},process:a=>{if(s)c=!0;else{if(s=!0,[t,n]=[n,t],n.length=0,o=t.length,o)for(let n=0;n<o;n++){const o=t[n];o(a),d.has(o)&&(r.schedule(o),e())}s=!1,c&&(c=!1,r.process(a))}}};return r}((()=>c=!0)),e)),{}),i=a.reduce(((e,t)=>{const n=f[t];return e[t]=(e,t=!1,o=!1)=>(c||h(),n.schedule(e,t,o)),e}),{}),u=a.reduce(((e,t)=>(e[t]=f[t].cancel,e)),{}),l=a.reduce(((e,t)=>(e[t]=()=>f[t].process(r),e)),{}),p=e=>f[e].process(r),m=e=>{c=!1,r.delta=s?t:Math.max(Math.min(e-r.timestamp,40),1),r.timestamp=e,d=!0,a.forEach(p),d=!1,c&&(s=!1,o(m))},h=()=>{c=!0,s=!0,d||o(m)};e.cancelSync=u,e.default=i,e.flushSync=l,e.getFrameData=()=>r,Object.defineProperty(e,"__esModule",{value:!0})}));
