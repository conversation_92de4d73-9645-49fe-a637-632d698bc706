@import './base.css';
@import "tailwindcss/preflight";
@import "tailwindcss/utilities";

/* Brutalist NZ Custom Properties */
:root {
  --color-base: #f5f7fa;
  --color-text: #1b1f23;
  --color-fern: #0d4225;
  --color-kawakawa: #1a5f3f;
  --color-charcoal: #111418;
  --color-mist: #e8eaed;

  --space-1: 0.5rem;
  --space-2: 1rem;
  --space-3: 1.5rem;
  --space-4: 2rem;
  --space-5: 2.5rem;
  --space-6: 3rem;
  --space-7: 3.5rem;
  --space-8: 4rem;
  --space-9: 4.5rem;
  --space-10: 5rem;
  --space-11: 5.5rem;
  --space-12: 6rem;
}

/* Typography */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@600&family=Inter:wght@400&display=swap');

/* Base styles */
body {
  font-family: 'Inter', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  color: var(--color-text);
  background-color: var(--color-base);
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
  line-height: 1.2;
}

h1 {
  font-size: clamp(1.75rem, 4vw, 2.25rem);
}

h2 {
  font-size: 1.75rem;
}

h3 {
  font-size: 1.5rem;
}

/* Micro-interactions */
.card-hover {
  transition: transform 0.2s ease-out, box-shadow 0.2s ease-out;
}

.card-hover:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.icon-rotate {
  transition: transform 0.2s ease-out;
}

.icon-rotate:hover {
  transform: rotate(2deg);
}

/* Fade in animation */
.fade-in {
  opacity: 0;
  animation: fadeIn 0.3s ease-out forwards;
}

@keyframes fadeIn {
  to {
    opacity: 1;
  }
}

/* Lazy image loading */
.lazy-image {
  filter: blur(5px);
  transition: filter 0.3s ease-out;
}

.lazy-image.loaded {
  filter: blur(0);
}

/* Dark spotlight sections */
.dark-spotlight {
  background-color: var(--color-charcoal);
  color: white;
}

.dark-spotlight .matariki-lines {
  stroke: rgba(255, 255, 255, 0.7);
}

/* Grid system */
.site-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

.grid-12 {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: 20px;
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  .card-hover:hover {
    transform: none;
  }

  .icon-rotate:hover {
    transform: none;
  }

  .fade-in {
    animation: none;
    opacity: 1;
  }

  .lazy-image {
    filter: none;
  }
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  font-weight: normal;
}

a,
.green {
  text-decoration: none;
  color: hsla(160, 100%, 37%, 1);
  transition: 0.4s;
  padding: 3px;
}

@media (hover: hover) {
  a:hover {
    background-color: hsla(160, 100%, 37%, 0.2);
  }
}

@media (min-width: 1024px) {
  body {
    display: flex;
    place-items: center;
  }

  #app {
    display: grid;
    grid-template-columns: 1fr 1fr;
    padding: 0 2rem;
  }
}
