# Brutalist NZ Site - MVP & Development Roadmap

## Questions & Clarifications Needed

### Content & Data Sources
1. **Substack Integration**: What's your Substack URL/RSS feed?
2. **Shortform Content**: Do you have existing content on YouTube, Instagram, Twitter/X that needs to be embedded?
3. **AI Help Guides**: Do you have existing content for these guides, or do they need to be written?
4. **Workshop Content**: Do you have existing workshops to list, or is this for future planning?

### Technical Preferences
1. **Hosting**: Any preference between Vercel, Netlify, or Cloudflare Pages?
2. **Domain**: Do you have a domain ready, or need help choosing one?
3. **Analytics**: Preference for analytics solution (mentioned Plausible)?
4. **Email Integration**: Do you have ConvertKit account or prefer another service?

### Design Assets
1. **Custom Icons**: Do you have existing brand assets or need custom NZ-influenced icons created?
2. **Photography**: Do you have existing photos that fit the aesthetic, or need stock/custom photos?
3. **Logo/Branding**: Any existing branding elements to incorporate?

---

## MVP Definition (Phase 1 - Launch Ready)

### Core Features (Must Have)
- [x] **Landing Page** with hero section and Matariki constellation
- [x] **Article Feed** pulling from Substack RSS
- [x] **Individual Article Pages** with proper typography
- [x] **Basic Navigation** between all main sections
- [x] **Contact Page** with social links and Ko-fi button
- [x] **Responsive Design** working on mobile/tablet/desktop
- [x] **Basic Matariki Animation** (static with scroll response)
- [x] **Performance Optimized** (< 2.5s LCP, proper image loading)

### Content Sections (MVP)
- [x] **Home** - Hero with latest 3 articles
- [x] **Articles** - Full feed with basic filtering
- [x] **AI Help** - Static index page (guides can be added later)
- [x] **Workshops** - Simple upcoming/past list from YAML
- [x] **Contact** - Links and coffee button

### Technical MVP Stack
- [x] **Vue 3** + Composition API
- [x] **Vite** for build tooling
- [x] **Tailwind CSS** + typography plugin
- [x] **Basic GSAP** for Matariki constellation
- [x] **Netlify/Vercel** deployment
- [x] **RSS parsing** for Substack integration

---

## Development Roadmap

### Phase 1: Foundation (Weeks 1-2)
**Goal**: Get basic site structure and core pages working

#### Week 1: Project Setup & Core Structure
- [ ] Initialize Vue 3 + Vite project
- [ ] Configure Tailwind CSS with custom design tokens
- [ ] Set up basic routing (Vue Router)
- [ ] Create base layout components (Header, Footer)
- [ ] Implement responsive grid system
- [ ] Set up deployment pipeline

#### Week 2: Core Pages & Content
- [ ] Build landing page with hero section
- [ ] Create article feed page with RSS integration
- [ ] Implement individual article page template
- [ ] Add basic navigation between pages
- [ ] Create contact page with social links

### Phase 2: Visual Polish & Interactions (Weeks 3-4)
**Goal**: Add the distinctive visual elements and micro-interactions

#### Week 3: Matariki & Animations
- [ ] Create Matariki SVG constellation
- [ ] Implement basic scroll-triggered animations
- [ ] Add micro-interactions (card hovers, icon rotations)
- [ ] Implement dark spotlight sections
- [ ] Add loading states and transitions

#### Week 4: Content Integration & Polish
- [ ] Integrate real Substack RSS feed
- [ ] Add shortform content section (if content available)
- [ ] Implement AI Help section structure
- [ ] Add workshop listing from YAML
- [ ] Performance optimization and testing

### Phase 3: Enhancement & Launch Prep (Week 5)
**Goal**: Final polish, testing, and launch preparation

- [ ] Accessibility testing and fixes
- [ ] SEO optimization (meta tags, sitemap)
- [ ] Cross-browser testing
- [ ] Performance audit and optimization
- [ ] Content review and final adjustments
- [ ] Launch!

---

## Post-MVP Enhancements (Phase 2 & 3)

### Phase 2 Features (Months 1-3)
- [ ] **Enhanced Matariki**: Time/season responsiveness
- [ ] **Search Functionality**: Article search with Algolia
- [ ] **Email Integration**: ConvertKit for workshop notifications
- [ ] **Analytics**: Plausible or similar privacy-focused analytics
- [ ] **Content Management**: Easier workshop management
- [ ] **Social Sharing**: Enhanced social cards

### Phase 3 Features (Months 3-6)
- [ ] **Workshop Booking**: Full booking system with payments
- [ ] **Comments System**: Giscus or similar for article comments
- [ ] **PWA Features**: Offline reading capabilities
- [ ] **Advanced Animations**: More sophisticated Matariki interactions
- [ ] **Content Expansion**: More AI guides and resources

---

## Technical Architecture Decisions

### Confirmed Choices
- **Framework**: Vue 3 (Composition API) ✅
- **Build Tool**: Vite ✅
- **Styling**: Tailwind CSS ✅
- **Animations**: VueUse Motion + GSAP ✅
- **State**: VueUse composables (Pinia if needed later) ✅

### To Decide
- **Hosting Platform**: Vercel vs Netlify vs Cloudflare Pages
- **CMS**: Netlify CMS vs manual YAML vs headless CMS
- **Analytics**: Plausible vs Simple Analytics vs Google Analytics
- **Email Service**: ConvertKit vs Mailchimp vs other

---

## Success Metrics

### Technical Goals
- **Performance**: Lighthouse score > 95
- **Accessibility**: WCAG 2.1 AA compliance
- **Core Web Vitals**: LCP < 2.5s, FID < 100ms, CLS < 0.1
- **Bundle Size**: Initial JS < 150KB

### Content Goals
- **Article Integration**: Seamless Substack RSS pulling
- **User Experience**: Intuitive navigation and reading experience
- **Cultural Integration**: Respectful and purposeful Aotearoa elements
- **Mobile Experience**: Excellent mobile performance and usability

---

## Next Steps

1. **Answer clarification questions** above
2. **Confirm MVP scope** - any features to add/remove?
3. **Choose technical decisions** (hosting, etc.)
4. **Set up development environment**
5. **Begin Phase 1 development**

Would you like to start with any specific phase or need clarification on any part of this roadmap?
