{"name": "eslint-plugin-playwright", "description": "ESLint plugin for Playwright testing.", "version": "2.2.0", "repository": "https://github.com/playwright-community/eslint-plugin-playwright", "author": "<PERSON> <<EMAIL>>", "packageManager": "pnpm@8.12.0", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "workspaces": ["examples"], "engines": {"node": ">=16.6.0"}, "type": "module", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "import": "./index.js", "require": "./index.cjs"}}, "files": ["dist", "index.js", "index.cjs", "index.d.ts"], "scripts": {"build": "tsup src/index.ts --format cjs --out-dir dist", "lint": "eslint .", "fmt": "prettier --write .", "fmt:check": "prettier --check .", "test": "vitest", "test:watch": "vitest --reporter=dot", "ts": "tsc --noEmit"}, "peerDependencies": {"eslint": ">=8.40.0"}, "dependencies": {"globals": "^13.23.0"}, "devDependencies": {"@mskelton/eslint-config": "^9.0.1", "@mskelton/semantic-release-config": "^1.0.1", "@types/estree": "^1.0.6", "@types/node": "^20.11.17", "@typescript-eslint/parser": "^8.11.0", "dedent": "^1.5.1", "eslint": "^9.13.0", "prettier": "^3.0.3", "prettier-plugin-jsdoc": "^1.3.0", "semantic-release": "^23.0.2", "tsup": "^8.0.1", "typescript": "^5.2.2", "vitest": "^1.3.1"}}